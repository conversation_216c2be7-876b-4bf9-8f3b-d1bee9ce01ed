<template>
  <div>
    <!-- Task List Header -->
    <div class="flex items-center justify-between">
      <label class="font-semibold text-sm"><PERSON>h sách công việc</label>
      <div
        @click="handleCreateWorkEffort"
        class="bg-primary text-white px-2 py-1 rounded flex items-center cursor-pointer"
      >
        {{
          dataWorkEffort[0]?.workflowId === "CHECK_IN"
            ? "Kết thúc ca"
            : "Bắt đầu ca"
        }}
      </div>
    </div>

    <!-- Task List -->
    <div class="space-y-3 mt-3 md:h-[60vh] h-[65vh] overflow-y-auto">
      <div
        v-for="task in dataWorkEffort"
        :key="task.id"
        @click="handleSelectWorkEffort(task)"
        class="rounded-lg p-3 transition-all duration-200 cursor-pointer"
        :class="[
          selectedWorkEffortId?.id === task.id && isMobile
            ? 'bg-primary text-white shadow-md border-primary'
            : 'bg-white border border-gray-200 hover:shadow-sm hover:border-gray-300',
        ]"
      >
        <!-- Task Header -->
        <div class="flex items-start justify-between mb-2">
          <div class="flex-1 min-w-0">
            <h4
              class="text-sm font-medium truncate"
              :class="
                selectedWorkEffortId?.id === task.id && isMobile
                  ? 'text-white'
                  : 'text-gray-900'
              "
            >
              {{ task?.name }}
            </h4>
            <p
              class="text-xs mt-1"
              :class="
                selectedWorkEffortId?.id === task.id && isMobile
                  ? 'text-white/80'
                  : 'text-gray-500'
              "
            ></p>
          </div>

          <!-- Selected indicator -->
          <div
            v-if="selectedWorkEffortId?.id === task.id && isMobile"
            class="flex items-center justify-center w-6 h-6 bg-white/20 rounded-full"
          >
            <svg
              class="w-3 h-3 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
        </div>

        <!-- Task Details -->
        <div class="space-y-2">
          <!-- Assignee -->
          <div class="flex items-center gap-2">
            <svg
              class="w-3 h-3"
              :class="
                selectedWorkEffortId?.id === task.id && isMobile
                  ? 'text-white/70'
                  : 'text-gray-400'
              "
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
            <span
              class="text-xs cursor-help"
              :class="
                selectedWorkEffortId?.id === task.id && isMobile
                  ? 'text-white/90'
                  : 'text-gray-600'
              "
            >
              {{ getEmployeeName(task?.createdBy) }}
            </span>
          </div>

          <!-- Time -->
          <div class="flex items-center gap-2">
            <svg
              class="w-3 h-3"
              :class="
                selectedWorkEffortId?.id === task.id && isMobile
                  ? 'text-white/70'
                  : 'text-gray-400'
              "
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span
              class="text-xs"
              :class="
                selectedWorkEffortId?.id === task.id && isMobile
                  ? 'text-white/90'
                  : 'text-gray-600'
              "
            >
              {{ formatTimestampV3(task?.createdStamp) }}
            </span>
          </div>

          <!-- Status -->
          <div class="flex items-center justify-between">
            <span
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
              :class="
                selectedWorkEffortId === task.id && isMobile
                  ? 'bg-white/20 text-white'
                  : 'bg-gray-100 text-gray-700'
              "
            >
              {{ task?.workEffortTypeId === "CHECK_IN" ? "Vào ca" : "Ra ca" }}
            </span>
          </div>
        </div>
        <!-- task editor -->
        <TiptapEditor
          v-if="selectedWorkEffortId && !isMobile"
          @update:modelValue="handleUpdateContent(task?.id)"
          :modelValue="handleGetContent(task?.id)"
          :placeholder="'Nhập nội dung công việc...'"
          :showCharacterCount="true"
          :editable="!isLoading"
          class="transition-opacity duration-200 h-[200px]"
          :class="{ 'opacity-50 pointer-events-none': isLoading }"
        ></TiptapEditor>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits([
  "createWorkEffort",
  "selectWorkEffort",
  "dataEmployee",
]);

const props = defineProps<{
  dataWorkEffort: any[];
  selectedWorkEffortId: any | null;
  isMobile: Boolean;
  dataEmployee?: any[];
}>();

// Computed properties
const getEmployeeName = (createdBy: string) => {
  if (!createdBy) return "Không xác định";

  if (!props.dataEmployee || !Array.isArray(props.dataEmployee)) {
    return createdBy; // Trả về createdBy gốc nếu không có dataEmployee
  }
  console.log("dataEmployee", props.dataEmployee);
  const employee = props.dataEmployee.find((emp: any) => {
    return emp.id === createdBy;
  });
  if (employee) {
    const employeeName = employee.name;
    if (employeeName !== createdBy && employeeName !== employee.id) {
      return employeeName;
    }
  }
  return createdBy;
};

// Functions
const handleCreateWorkEffort = () => {
  emit("createWorkEffort");
};

const handleSelectWorkEffort = (workEffort: any) => {
  if (!props.isMobile) return;
  emit("selectWorkEffort", workEffort);
};
// Methods
const { getContent, handleSaveTextEditor } = usePortal();
const isLoading = ref(false);
const contentData = ref<any>(null);

const handleUpdateContent = async (content: string) => {
  if (!props.selectedWorkEffortId?.id) return;

  const data = {
    type: "DESCRIPTION_WORKEFFORT",
    content: content,
    relativeId: props.selectedWorkEffortId?.id,
    version: new Date(),
    createdBy: "hung",
  };

  try {
    await handleSaveTextEditor(data);
  } catch (error) {
    throw error;
  }
};

const handleGetContent = async (id: string) => {
  if (!id) {
    return '';
  }

  try {
    isLoading.value = true;
    const response = await getContent("DESCRIPTION_WORKEFFORT", id);

    contentData.value = response;
    return response?.content;
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};
onMounted(async () => {
  if (!props.isMobile) {
    await handleGetContent();
  }
});
</script>

<style scoped>
/* Custom scrollbar */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}
</style>
