<template>
  <div class="bg-white rounded-lg border border-gray-200 md:h-screen-50">
    <div class="p-2 space-y-2">
      <!-- Custom Date Range -->
      <div>
        <label class="block text-sm text-gray-700 mb-2 font-medium">
          <PERSON><PERSON><PERSON><PERSON> thời gian
        </label>
        <div class="relative">
          <flat-pickr
            v-model="dateRange"
            :config="datePickerConfig"
            class="w-full px-3 py-2 pr-16 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
            placeholder="Chọn khoảng thời gian..."
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-1">
            <!-- Clear button -->
            <button
              v-if="dateRange"
              @click="clearDateRange"
              class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              type="button"
            >
              <svg
                class="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
            <!-- Calendar icon -->
            <div class="p-1 pointer-events-none">
              <svg
                class="w-4 h-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Employee Select -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Chọn nhân viên
        </label>
        <div class="relative">
          <button
            @click="toggleEmployeeDropdown"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm text-left bg-white flex items-center justify-between hover:border-gray-400 transition-colors"
            :class="{
              'border-primary ring-2 ring-primary/20': isEmployeeDropdownOpen,
            }"
          >
            <div class="flex items-center gap-2 flex-1 min-w-0">
              <svg
                class="w-4 h-4 text-gray-400 flex-shrink-0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
              <span
                class="truncate"
                :class="selectedEmployee ? 'text-gray-900' : 'text-gray-500'"
              >
                {{
                  selectedEmployee
                    ? selectedEmployee.name ||
                      selectedEmployee.fullName ||
                      selectedEmployee.employeeName
                    : "Chọn nhân viên..."
                }}
              </span>
            </div>

            <svg
              :class="[
                'w-4 h-4 text-gray-400 transition-transform flex-shrink-0',
                isEmployeeDropdownOpen ? 'rotate-180' : '',
              ]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>

          <!-- Dropdown Menu -->
          <div
            v-if="isEmployeeDropdownOpen"
            class="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto"
          >
            <!-- Employee List -->
            <div class="py-1">
              <!-- All Employees Option -->
              <button
                @click="selectEmployee(null)"
                class="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 transition-colors"
              >
                <svg
                  class="w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
                <span>Tất cả nhân viên</span>
              </button>

              <!-- Individual Employees -->
              <button
                v-for="employee in filteredEmployees"
                :key="employee.id || employee.employeeId"
                @click="selectEmployee(employee)"
                class="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 transition-colors"
              >
                <svg
                  class="w-4 h-4 text-gray-400 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
                <div class="flex-1 min-w-0">
                  <div class="font-medium truncate">
                    {{
                      employee.name ||
                      employee.fullName ||
                      employee.employeeName
                    }}
                  </div>
                  <div
                    v-if="employee.department || employee.position"
                    class="text-xs text-gray-500 truncate"
                  >
                    {{ employee.department || employee.position }}
                  </div>
                </div>
              </button>

              <!-- No Results -->
              <div
                v-if="filteredEmployees.length === 0 && employeeSearchQuery"
                class="px-3 py-4 text-center text-sm text-gray-500"
              >
                <svg
                  class="mx-auto w-8 h-8 text-gray-300 mb-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
                <p>Không tìm thấy nhân viên nào</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- task -->
      <div class="border"></div>

      <!-- Task List Component -->
      <TaskList
        class="md:hidden block"
        :dataWorkEffort="dataWorkEffort"
        :selectedWorkEffortId="selectedWorkEffortId"
        :isMobile="false"
        :dataEmployee="dataEmployee"
        @createWorkEffort="handleCreateWorkEffort"
        @selectWorkEffort="handleSelectWorkEffort"
      />
      <TaskList
        class="md:block hidden"
        :dataWorkEffort="dataWorkEffort"
        :selectedWorkEffortId="selectedWorkEffortId"
        :isMobile="true"
        :dataEmployee="dataEmployee"
        @createWorkEffort="handleCreateWorkEffort"
        @selectWorkEffort="handleSelectWorkEffort"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// Import flat-pickr
import FlatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { Vietnamese } from "flatpickr/dist/l10n/vn.js";
import type { Options } from "flatpickr/dist/types/options";

// Import TaskList component
import TaskList from "./TaskList.vue";

const emit = defineEmits([
  "filtersChanged",
  "employeeSelected",
  "dateRangeChanged",
  "createWorkEffort",
  "selectWorkEffort",
  "dataEmployee",
]);

const props = defineProps<{
  dataWorkEffort: any[];
  selectedWorkEffortId: any | null;
  dataEmployee?: any[];
}>();
const authCookie = useCookie("auth") as any;

// Reactive data
const selectedEmployee = ref<any | null>(null);
const isEmployeeDropdownOpen = ref(false);
const employeeSearchQuery = ref("");

// Date range data
const dateRange = ref<string[]>([]);

// Flatpickr configuration - following SearchOrder pattern
const datePickerConfig = ref<Partial<Options>>({
  locale: Vietnamese,
  mode: "range",
  dateFormat: "d-m-Y",
  onChange: (selectedDates: any) => {
    if (selectedDates.length === 2) {
      handleChangeDate(
        selectedDates[1].toISOString(),
        selectedDates[0].toISOString()
      );
    }
  },
});

// Computed properties
const filteredEmployees = computed(() => {
  if (!props.dataEmployee || !Array.isArray(props.dataEmployee)) {
    return [];
  }

  if (!employeeSearchQuery.value.trim()) {
    return props.dataEmployee;
  }

  const query = employeeSearchQuery.value.toLowerCase().trim();
  return props.dataEmployee.filter((employee: any) => {
    const name = (
      employee.name ||
      employee.fullName ||
      employee.employeeName ||
      ""
    ).toLowerCase();
    const department = (employee.department || "").toLowerCase();
    const position = (employee.position || "").toLowerCase();

    return (
      name.includes(query) ||
      department.includes(query) ||
      position.includes(query)
    );
  });
});

// Functions
const handleChangeDate = (dateTo: string, dateFrom: string) => {
  const dateRangeData = {
    dateTo: dateTo,
    dateFrom: dateFrom,
    startDate: new Date(dateFrom),
    endDate: new Date(dateTo),
  };

  emit("dateRangeChanged", dateRangeData);
};

const clearDateRange = () => {
  dateRange.value = [];

  emit("dateRangeChanged", {
    dateTo: null,
    dateFrom: null,
    startDate: null,
    endDate: null,
  });
};

const autoSelectCurrentUser = () => {
  if (
    !props.dataEmployee ||
    !Array.isArray(props.dataEmployee) ||
    selectedEmployee.value
  ) {
    return;
  }

  try {
    const authUser = authCookie.value?.user;
    if (!authUser?.id) {
      return;
    }

    // Tìm nhân viên có id trùng với auth.user.id
    const currentUserEmployee = props.dataEmployee.find((employee: any) => {
      return (
        employee.id === authUser.id ||
        employee.employeeId === authUser.id ||
        employee.userId === authUser.id
      );
    });

    if (currentUserEmployee) {
      selectedEmployee.value = currentUserEmployee;
      // Emit selected employee
      emit("employeeSelected", currentUserEmployee);
    }
  } catch (error) {
    console.warn("Error auto-selecting current user:", error);
  }
};

const toggleEmployeeDropdown = () => {
  isEmployeeDropdownOpen.value = !isEmployeeDropdownOpen.value;
  if (isEmployeeDropdownOpen.value) {
    employeeSearchQuery.value = "";
  }
};

const selectEmployee = (employee: any | null) => {
  selectedEmployee.value = employee;
  isEmployeeDropdownOpen.value = false;
  employeeSearchQuery.value = "";

  // Emit selected employee
  emit("employeeSelected", employee);
};

const handleCreateWorkEffort = () => {
  emit("createWorkEffort");
};

const handleSelectWorkEffort = (workEffort: string) => {
  emit("selectWorkEffort", workEffort);
};

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  if (!target.closest(".relative")) {
    isEmployeeDropdownOpen.value = false;
  }
};

// Watchers
watch(
  () => props.dataEmployee,
  () => {
    // Tự động chọn user hiện tại khi dataEmployee thay đổi
    nextTick(() => {
      autoSelectCurrentUser();
    });
  },
  { immediate: true }
);

// Lifecycle hooks
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
  // Tự động chọn user hiện tại khi component mount
  nextTick(() => {
    autoSelectCurrentUser();
  });
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>
