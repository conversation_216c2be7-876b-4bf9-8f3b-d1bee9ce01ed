export default function useCrm() {
  const $sdk = useNuxtApp().$sdk;
  const getWorkEffort = async (
    performerId: string,
    workEffortTypeId: string,
    source: string,
    fromDate: string,
    toDate: string
  ) => {
    try {
      const response = await $sdk.crm.getWorkEffortsV2(
        performerId,
        workEffortTypeId,
        source,
        fromDate,
        toDate
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const createWorkEffort = async (
    createdBy: string,
    name: string,
    decription: string,
    workEffortTypeId: string,
    source: string
  ) => {
    try {
      const response = await $sdk.crm.createWorkEffortV2(
        createdBy,
        name,
        decription,
        workEffortTypeId,
        source
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    getWorkEffort,
    createWorkEffort,
  };
}
