<template>
  <div class="h-full flex flex-col bg-white">
    <!-- Header -->
    <div class="flex-shrink-0 px-6 py-4 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900">H<PERSON><PERSON> <PERSON><PERSON> kèm</h2>
      <p v-if="dataImage.length > 0" class="text-sm text-gray-500 mt-1">
        {{ dataImage.length }} hình ảnh
      </p>
    </div>

    <!-- Content -->
    <div class="flex-1 overflow-y-auto">
      <!-- Images Grid -->
      <div v-if="dataImage.length > 0" class="p-6">
        <div
          class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
        >
          <div
            v-for="(image, index) in dataImage"
            :key="image.id || index"
            class="group relative aspect-square bg-gray-100 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200"
          >
            <!-- Image -->
            <NuxtImg
              :src="getImageUrl(image)"
              :alt="`Hình ảnh ${index + 1}`"
              class="w-full h-full object-cover cursor-pointer transition-transform duration-200 group-hover:scale-105"
              @click="openImageModal(getImageUrl(image), index)"
              @error="handleImageError($event, index)"
              loading="lazy"
            />
            <!-- Image Index -->
            <div
              class="absolute top-2 left-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded"
            >
              {{ index + 1 }}
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="flex-1 flex items-center justify-center p-6">
        <div class="text-center max-w-md mx-auto">
          <!-- Animated Camera Icon -->
          <div class="relative mb-8">
            <div
              class="w-24 h-24 mx-auto bg-gradient-to-br from-primary-light to-primary rounded-full flex items-center justify-center shadow-lg"
            >
              <svg
                class="w-12 h-12 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
            </div>
            <!-- Pulse animation -->
            <div
              class="absolute inset-0 w-24 h-24 mx-auto bg-primary rounded-full animate-ping opacity-20"
            ></div>
          </div>

          <!-- Content -->
          <div class="space-y-4">
            <h3 class="text-xl font-semibold text-gray-900">
              Chưa có hình ảnh chấm công
            </h3>
            <p class="text-gray-500 leading-relaxed">
              Hãy chụp ảnh để ghi lại thời gian làm việc của bạn. Ảnh sẽ được
              lưu trữ an toàn và giúp xác thực thời gian chấm công.
            </p>
          </div>

          <!-- Action Buttons -->
          <div class="mt-8 space-y-3">
            <button
              @click="isOpenCamera = true"
              class="w-full bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary text-white font-medium py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center justify-center space-x-3"
            >
              <svg
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
              <span>Chụp ảnh chấm công</span>
            </button>

            <!-- Info Card -->
            <div
              class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left"
            >
              <div class="flex items-start space-x-3">
                <svg
                  class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <div class="text-sm text-blue-700">
                  <p class="font-medium mb-1">Lưu ý quan trọng:</p>
                  <ul class="space-y-1 text-blue-600">
                    <li>• Đảm bảo khuôn mặt rõ ràng trong ảnh</li>
                    <li>• Chụp ảnh tại vị trí làm việc</li>
                    <li>• Ảnh sẽ được mã hóa và bảo mật</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Image Modal -->
    <Teleport to="body">
      <div
        v-if="selectedImage"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm"
        @click="closeImageModal"
      >
        <div class="relative max-w-7xl max-h-full p-4 w-full">
          <!-- Close Button -->
          <button
            @click="closeImageModal"
            class="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full transition-all duration-200"
          >
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>

          <!-- Navigation Buttons -->
          <button
            v-if="dataImage.length > 1"
            @click.stop="previousImage"
            class="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full transition-all duration-200"
          >
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>

          <button
            v-if="dataImage.length > 1"
            @click.stop="nextImage"
            class="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full transition-all duration-200"
          >
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>

          <!-- Image -->
          <div class="flex items-center justify-center h-full">
            <NuxtImg
              :src="selectedImage.url"
              :alt="selectedImage.alt"
              class="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
              @click.stop
            />
          </div>

          <!-- Image Info -->
          <div
            class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-4 py-2 rounded-full text-sm"
          >
            {{ selectedImage.index + 1 }} / {{ dataImage.length }}
          </div>
        </div>
      </div>
    </Teleport>
    <ModalTimeKeeping
      v-if="isOpenCamera"
      @isClose="isOpenCamera = false"
      @uploadImage="handleUploadImage"
    />
  </div>
  <LoadingSpinner v-if="isLoading"></LoadingSpinner>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from "vue";

const props = defineProps(["selectedWorkEffortId"]);
const { getUrlImageTimeKeeping } = useTimeKeeping();
const isOpenCamera = ref(false);
// Reactive data
const selectedImage = ref<{ url: string; alt: string; index: number } | null>(
  null
);

// Function to get image URL from dataImage item
const getImageUrl = (image: any) => {
  if (!image) return "";

  // Nếu image có trường url trực tiếp
  if (image.url) {
    return image.url;
  }

  // Nếu image có srcPath, tạo URL từ endpoint
  if (image.srcPath) {
    const endpoint = getUrlImageTimeKeeping();
    return `${endpoint}/${image.srcPath}`;
  }

  // Nếu image có path
  if (image.path) {
    const endpoint = getUrlImageTimeKeeping();
    return `${endpoint}/${image.path}`;
  }

  // Fallback: return image object as string nếu nó là URL
  if (typeof image === "string") {
    return image;
  }

  return "";
};

// Functions
const openImageModal = (imageUrl: string, index: number) => {
  selectedImage.value = {
    url: imageUrl,
    alt: `Hình ảnh ${index + 1}`,
    index,
  };
};

const closeImageModal = () => {
  selectedImage.value = null;
};

const nextImage = () => {
  if (!selectedImage.value || dataImage.value.length <= 1) return;

  const currentIndex = selectedImage.value.index;
  const nextIndex = (currentIndex + 1) % dataImage.value.length;
  const nextImageData = dataImage.value[nextIndex];

  selectedImage.value = {
    url: getImageUrl(nextImageData),
    alt: `Hình ảnh ${nextIndex + 1}`,
    index: nextIndex,
  };
};

const previousImage = () => {
  if (!selectedImage.value || dataImage.value.length <= 1) return;

  const currentIndex = selectedImage.value.index;
  const prevIndex =
    currentIndex === 0 ? dataImage.value.length - 1 : currentIndex - 1;
  const prevImageData = dataImage.value[prevIndex];

  selectedImage.value = {
    url: getImageUrl(prevImageData),
    alt: `Hình ảnh ${prevIndex + 1}`,
    index: prevIndex,
  };
};

const handleImageError = (event: Event, index: number) => {
  const target = event.target as HTMLImageElement;
  console.error(`Lỗi tải hình ảnh ${index + 1}:`, target.src);

  // Thay thế bằng placeholder hoặc ẩn hình ảnh
  target.style.display = "none";

  // Hoặc có thể thay thế bằng placeholder image
  // target.src = '/images/placeholder-image.png';
};

// Keyboard navigation
const handleKeydown = (event: KeyboardEvent) => {
  if (!selectedImage.value) return;

  switch (event.key) {
    case "Escape":
      closeImageModal();
      break;
    case "ArrowLeft":
      previousImage();
      break;
    case "ArrowRight":
      nextImage();
      break;
  }
};
const { uploadImage, getImage } = useFileService();
const isLoading = ref<Boolean>(false);
const handleUploadImage = async (Image: any) => {
  isLoading.value = true;
  try {
    await uploadImage(
      Image,
      "public",
      props.selectedWorkEffortId.id,
      "WORK_EFFORT_TIMEKEEPING"
    );
    await handleGetImage();
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};
const dataImage = ref<any>([]);
const handleGetImage = async () => {
  try {
    const response = await getImage(
      props.selectedWorkEffortId?.id,
      "WORK_EFFORT_TIMEKEEPING"
    );
    dataImage.value = response;
  } catch (error) {
    throw error;
  }
};
// const handleGetUrlImage = ()
// Watch for selectedWorkEffortId changes
watch(
  () => props.selectedWorkEffortId?.id,
  async (newId) => {
    if (newId) {
      await handleGetImage();
    } else {
      dataImage.value = [];
    }
  },
  { immediate: true }
);

// Lifecycle hooks
onMounted(async () => {
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown);
});
</script>
